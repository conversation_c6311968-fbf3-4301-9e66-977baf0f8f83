<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置验证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #4facfe;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #3d8bfe;
        }
        .test-result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        .success {
            color: #155724;
            background: #d4edda;
            border-color: #c3e6cb;
        }
        .form-group {
            margin: 10px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>LLM配置验证测试</h1>
    
    <div class="test-section">
        <h2>模拟LLM配置</h2>
        <div class="form-group">
            <label>平台:</label>
            <select id="test-platform">
                <option value="openai">OpenAI</option>
                <option value="anthropic">Anthropic</option>
                <option value="google">Google</option>
            </select>
        </div>
        <div class="form-group">
            <label>模型:</label>
            <input type="text" id="test-model" value="gpt-4-vision-preview" placeholder="输入模型名称">
        </div>
        <div class="form-group">
            <label>API Key:</label>
            <input type="text" id="test-api-key" placeholder="输入API Key">
        </div>
        <div class="form-group">
            <label>使用自定义模型:</label>
            <input type="checkbox" id="test-use-custom">
        </div>
        <div class="form-group">
            <label>自定义模型名称:</label>
            <input type="text" id="test-custom-model" placeholder="例如: gemini-2.0-flash-exp">
        </div>
        
        <button class="test-button" onclick="testConfigValidation()">测试配置验证</button>
        <div id="validation-result" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>多平台API Key测试</h2>
        <button class="test-button" onclick="testMultiPlatformApiKeys()">测试多平台API Key</button>
        <div id="multiplatform-result" class="test-result"></div>
    </div>

    <!-- 引入所有模块 -->
    <script src="src/config.js"></script>
    <script src="src/model-manager.js"></script>

    <script>
        function testConfigValidation() {
            const result = document.getElementById('validation-result');
            let output = '配置验证测试结果:\n';
            
            try {
                const configManager = new ConfigManager();
                
                // 获取测试配置
                const platform = document.getElementById('test-platform').value;
                const model = document.getElementById('test-model').value;
                const apiKey = document.getElementById('test-api-key').value;
                const useCustom = document.getElementById('test-use-custom').checked;
                const customModel = document.getElementById('test-custom-model').value;
                
                // 构建测试配置
                const testConfig = {
                    service: 'llm',
                    llm: {
                        platform: platform,
                        model: useCustom ? customModel : model,
                        useCustomModel: useCustom,
                        customModel: customModel,
                        apiKeys: {
                            [platform]: apiKey
                        }
                    }
                };
                
                output += `测试配置:\n`;
                output += `  平台: ${platform}\n`;
                output += `  模型: ${useCustom ? customModel : model}\n`;
                output += `  API Key: ${apiKey ? '已设置' : '未设置'}\n`;
                output += `  使用自定义模型: ${useCustom}\n\n`;
                
                // 测试验证
                const validation = configManager.validateConfig(testConfig);
                output += `验证结果: ${validation.valid ? '✓ 通过' : '✗ 失败'}\n`;
                
                if (!validation.valid) {
                    output += `错误信息: ${validation.error}\n`;
                    result.className = 'test-result error';
                } else {
                    output += `配置有效，可以进行OCR识别\n`;
                    
                    // 测试服务配置获取
                    const serviceConfig = configManager.getServiceConfig(testConfig, 'llm');
                    output += `\n服务配置:\n`;
                    output += `  平台: ${serviceConfig.platform}\n`;
                    output += `  最终模型: ${serviceConfig.model}\n`;
                    output += `  API Key: ${serviceConfig.apiKey ? '已获取' : '未获取'}\n`;
                    
                    result.className = 'test-result success';
                }
                
            } catch (error) {
                output += `✗ 测试出错: ${error.message}\n`;
                result.className = 'test-result error';
            }
            
            result.textContent = output;
        }

        function testMultiPlatformApiKeys() {
            const result = document.getElementById('multiplatform-result');
            let output = '多平台API Key测试结果:\n';
            
            try {
                const configManager = new ConfigManager();
                
                // 测试配置，包含多个平台的API Key
                const testConfig = {
                    service: 'llm',
                    llm: {
                        platform: 'openai',
                        model: 'gpt-4-vision-preview',
                        apiKeys: {
                            openai: 'sk-test-openai-key',
                            anthropic: 'sk-ant-test-key',
                            google: 'AIza-test-google-key',
                            custom: 'custom-test-key'
                        }
                    }
                };
                
                // 测试不同平台的配置获取
                const platforms = ['openai', 'anthropic', 'google'];
                
                platforms.forEach(platform => {
                    testConfig.llm.platform = platform;
                    
                    const validation = configManager.validateConfig(testConfig);
                    const serviceConfig = configManager.getServiceConfig(testConfig, 'llm');
                    
                    output += `${platform}平台:\n`;
                    output += `  验证: ${validation.valid ? '✓ 通过' : '✗ 失败'}\n`;
                    output += `  API Key: ${serviceConfig.apiKey}\n`;
                    output += `  期望: ${testConfig.llm.apiKeys[platform]}\n`;
                    output += `  匹配: ${serviceConfig.apiKey === testConfig.llm.apiKeys[platform] ? '✓' : '✗'}\n\n`;
                });
                
                result.className = 'test-result success';
                
            } catch (error) {
                output += `✗ 测试出错: ${error.message}\n`;
                result.className = 'test-result error';
            }
            
            result.textContent = output;
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', () => {
            // 设置一些默认值
            document.getElementById('test-api-key').value = 'sk-test-key-123';
            setTimeout(() => {
                testConfigValidation();
                testMultiPlatformApiKeys();
            }, 500);
        });
    </script>
</body>
</html>
