<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR状态管理测试</title>
    <link rel="stylesheet" href="assets/style.css">
    <style>
        .test-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
            margin: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        .test-button {
            margin: 10px;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            background: #4facfe;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #3d8bfe;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status-info {
            margin: 10px 0;
            padding: 10px;
            border-radius: 6px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="test-section">
        <h2>OCR状态管理测试</h2>
        
        <div class="status-info">
            <strong>当前状态:</strong> <span id="current-status">就绪</span><br>
            <strong>加载状态:</strong> <span id="loading-status">否</span><br>
            <strong>按钮状态:</strong> <span id="button-status">启用</span>
        </div>
        
        <div>
            <button class="test-button" onclick="testShowLoading()">测试显示加载</button>
            <button class="test-button" onclick="testHideLoading()">测试隐藏加载</button>
            <button class="test-button" onclick="testShowError()">测试显示错误</button>
            <button class="test-button" onclick="testConfigValidation()">测试配置验证失败</button>
            <button class="test-button" onclick="testOCRError()">测试OCR识别失败</button>
            <button class="test-button" onclick="test429Error()">测试429错误</button>
        </div>
        
        <div id="result-container" class="result-container" style="display: none;">
            <div class="result-header">
                <h3>识别结果</h3>
                <div class="result-actions">
                    <label class="checkbox-label">
                        <input type="checkbox" id="remove-linebreaks"> 去除换行符
                    </label>
                    <button id="copy-btn" class="btn-small">复制</button>
                    <button id="clear-btn" class="btn-small">清空</button>
                </div>
            </div>
            <textarea id="result-text" class="result-text" placeholder="识别结果将显示在这里，您可以直接编辑..."></textarea>
        </div>
        
        <div id="loading" class="loading" style="display: none;">
            <div class="spinner"></div>
            <p>正在处理中...</p>
        </div>
    </div>

    <!-- 模拟uTools API -->
    <script>
        // 模拟uTools API
        window.ocrAPI = {
            copyText: (text) => {
                console.log('复制文本:', text);
                navigator.clipboard?.writeText(text);
            },
            hideMainWindow: () => console.log('隐藏主窗口'),
            screenCapture: (callback) => {
                console.log('模拟截图');
                setTimeout(() => {
                    // 模拟截图成功
                    callback('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
                }, 1000);
            },
            db: {
                get: (id) => null,
                put: (doc) => ({ ok: true }),
                remove: (id) => ({ ok: true })
            }
        };
    </script>

    <!-- 引入所有模块 -->
    <script src="src/config.js"></script>
    <script src="src/ocr-services.js"></script>
    <script src="src/ui.js"></script>
    <script src="src/model-manager.js"></script>
    <script src="src/main.js"></script>

    <script>
        // 测试函数
        function updateStatus() {
            const uiManager = window.ocrPlugin?.uiManager;
            if (uiManager) {
                document.getElementById('current-status').textContent = uiManager.currentView || '未知';
                document.getElementById('loading-status').textContent = uiManager.isLoading ? '是' : '否';
                
                const buttons = document.querySelectorAll('button');
                const enabledButtons = Array.from(buttons).filter(btn => !btn.disabled).length;
                const totalButtons = buttons.length;
                document.getElementById('button-status').textContent = `${enabledButtons}/${totalButtons} 启用`;
            }
        }

        function testShowLoading() {
            console.log('测试显示加载状态');
            window.ocrPlugin?.uiManager?.showLoading('测试加载中...');
            updateStatus();
        }

        function testHideLoading() {
            console.log('测试隐藏加载状态');
            window.ocrPlugin?.uiManager?.hideLoading();
            updateStatus();
        }

        function testShowError() {
            console.log('测试显示错误');
            window.ocrPlugin?.uiManager?.showError('这是一个测试错误消息');
            updateStatus();
        }

        function testConfigValidation() {
            console.log('测试配置验证失败');
            // 模拟配置验证失败的情况
            window.ocrPlugin?.uiManager?.showLoading('正在验证配置...');
            setTimeout(() => {
                window.ocrPlugin?.uiManager?.showError('配置验证失败：请先配置OCR服务');
                updateStatus();
            }, 1000);
        }

        function testOCRError() {
            console.log('测试OCR识别失败');
            window.ocrPlugin?.uiManager?.showLoading('正在识别文字...');
            setTimeout(() => {
                window.ocrPlugin?.uiManager?.showError('OCR识别失败：无法连接到服务器');
                updateStatus();
            }, 2000);
        }

        function test429Error() {
            console.log('测试429错误');
            window.ocrPlugin?.uiManager?.showLoading('正在识别文字...');
            setTimeout(() => {
                window.ocrPlugin?.uiManager?.showError('API请求过于频繁，请稍后再试');
                updateStatus();
            }, 1500);
        }

        // 定期更新状态显示
        setInterval(updateStatus, 500);

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            setTimeout(updateStatus, 1000);
        });
    </script>
</body>
</html>
