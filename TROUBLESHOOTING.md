# 故障排除指南

## 🔧 常见问题解决方案

### 问题1: "请先完善配置信息"提示

**症状**: 已经配置了LLM相关信息，但测试连接或OCR识别时仍提示"请先完善配置信息"

**原因**: 
- 配置验证逻辑使用了旧的配置结构
- 当前平台的API Key未正确设置
- 自定义模型配置不完整

**解决方案**:
1. **检查当前平台API Key**:
   - 确保在当前选中的平台下输入了API Key
   - 切换平台时API Key会自动切换，确保每个平台都有对应的密钥

2. **验证模型配置**:
   - 如果使用自定义模型，确保输入了模型名称
   - 如果使用预设模型，确保从下拉列表中选择了模型

3. **重新保存配置**:
   - 点击"保存配置"按钮
   - 然后再次尝试"测试连接"

**验证步骤**:
```
1. 选择平台 (如: OpenAI)
2. 输入API Key (如: sk-xxx)
3. 选择模型 (如: gpt-4-vision-preview)
4. 点击"保存配置"
5. 点击"测试连接"
```

### 问题2: API Key显示错误平台的密钥

**症状**: 切换平台时，API Key输入框显示的是其他平台的密钥

**原因**: 平台切换时API Key未正确更新

**解决方案**:
1. **手动清空并重新输入**:
   - 切换到目标平台
   - 清空API Key输入框
   - 输入正确的API Key

2. **使用显示/隐藏功能验证**:
   - 点击眼睛图标显示API Key明文
   - 确认显示的是正确平台的密钥

### 问题3: 模型列表加载失败

**症状**: 选择平台后，模型下拉框显示"获取模型列表失败"

**原因**: 
- API Key无效或网络问题
- API调用频率限制
- 平台API服务异常

**解决方案**:
1. **验证API Key**:
   - 确保API Key格式正确
   - 在对应平台官网验证密钥有效性

2. **检查网络连接**:
   - 确保网络正常
   - 检查是否需要代理设置

3. **使用默认模型列表**:
   - 即使API调用失败，插件会显示默认模型列表
   - 可以从默认列表中选择模型

4. **手动刷新**:
   - 点击"刷新"按钮重新获取模型列表

### 问题4: 自定义模型无法使用

**症状**: 输入自定义模型名称后，测试连接失败

**原因**:
- 模型名称格式错误
- 模型不存在或未发布
- API权限不足

**解决方案**:
1. **验证模型名称格式**:
   - OpenAI: `gpt-4-vision-preview`
   - Anthropic: `claude-3-5-sonnet-20241022`
   - Google: `gemini-2.0-flash-exp`

2. **检查模型可用性**:
   - 确认模型已正式发布或在测试期间可用
   - 检查API账户是否有访问权限

3. **使用预设模型测试**:
   - 先用预设模型测试连接
   - 确认基本配置正确后再尝试自定义模型

### 问题5: OCR识别失败

**症状**: 配置正确但OCR识别时报错

**原因**:
- 图片格式不支持
- 图片过大或过小
- API调用限制
- 网络超时

**解决方案**:
1. **检查图片格式**:
   - 支持: PNG, JPG, JPEG, GIF, BMP
   - 建议使用PNG或JPG格式

2. **调整图片大小**:
   - 建议图片大小在1MB以内
   - 分辨率不要过高

3. **检查API配额**:
   - 确认API账户有足够余额
   - 检查调用频率限制

4. **尝试不同服务**:
   - 如果一个OCR服务失败，尝试其他服务
   - 比较不同服务的识别效果

## 🔍 调试技巧

### 1. 使用浏览器开发者工具
- 按F12打开开发者工具
- 查看Console标签页的错误信息
- 检查Network标签页的API调用情况

### 2. 测试页面验证
- 打开`config-test.html`进行配置验证测试
- 使用`test.html`进行功能模块测试

### 3. 配置导出导入
- 导出当前配置进行备份
- 重置配置后重新导入

### 4. 逐步排查
1. 先测试基础OCR服务（如百度OCR）
2. 再测试LLM视觉模型
3. 最后测试自定义模型

## 📞 获取帮助

如果以上方案都无法解决问题，请：

1. **收集错误信息**:
   - 浏览器控制台错误日志
   - 具体的错误提示信息
   - 使用的配置参数

2. **提供环境信息**:
   - 操作系统版本
   - uTools版本
   - 插件版本

3. **描述复现步骤**:
   - 详细的操作步骤
   - 期望结果和实际结果

---

**提示**: 大多数问题都与配置相关，请仔细检查每个平台的API Key设置。
